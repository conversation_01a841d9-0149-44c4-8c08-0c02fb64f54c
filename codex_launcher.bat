@echo off
chcp 65001 >nul
title Codex CLI Launcher

echo ========================================
echo           Codex CLI Launcher
echo ========================================
echo.

REM Set environment variables
set PATH=%PATH%;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm

echo Checking Codex CLI installation...
echo.

REM Check Node.js
echo [1/3] Checking Node.js...
node --version 2>nul
if %errorlevel% neq 0 (
    echo X Node.js not found
    goto error
) else (
    echo + Node.js installed
)

echo.

REM Check npm
echo [2/3] Checking npm...
npm --version 2>nul
if %errorlevel% neq 0 (
    echo X npm not found
    goto error
) else (
    echo + npm installed
)

echo.

REM Check codex
echo [3/3] Checking Codex CLI...
codex --version 2>nul
if %errorlevel% neq 0 (
    echo X Codex CLI not found
    goto error
) else (
    echo + Codex CLI installed
)

echo.
echo ========================================
echo           Installation OK!
echo ========================================
echo.
echo Available commands:
echo   codex                    - Start interactive session
echo   codex "your question"    - Ask directly
echo   codex login              - Login to account
echo   codex --help             - Show help
echo.
echo Press any key to start Codex CLI...
pause >nul

echo.
echo Starting Codex CLI...
codex

goto end

:error
echo.
echo ========================================
echo              ERROR!
echo ========================================
echo Please check if installation completed correctly
echo.

:end
echo.
echo Press any key to exit...
pause >nul
