@echo off
echo ========================================
echo           Codex CLI 测试工具
echo ========================================
echo.

REM 设置环境变量
set PATH=%PATH%;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm

echo 正在检查 Codex CLI 安装状态...
echo.

REM 检查 Node.js
echo [1/3] 检查 Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js 未找到
    goto error
) else (
    echo ✅ Node.js 已安装
)

echo.

REM 检查 npm
echo [2/3] 检查 npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm 未找到
    goto error
) else (
    echo ✅ npm 已安装
)

echo.

REM 检查 codex
echo [3/3] 检查 Codex CLI...
codex --version
if %errorlevel% neq 0 (
    echo ❌ Codex CLI 未找到或未正确配置
    goto error
) else (
    echo ✅ Codex CLI 已安装
)

echo.
echo ========================================
echo           安装检查完成！
echo ========================================
echo.
echo 您现在可以使用以下命令：
echo   codex                    - 启动交互式会话
echo   codex "你的问题"         - 直接提问  
echo   codex login              - 登录账户
echo   codex --help             - 查看帮助
echo.
echo 按任意键启动 Codex CLI 交互式会话...
pause >nul

echo.
echo 正在启动 Codex CLI...
codex

goto end

:error
echo.
echo ========================================
echo              出现错误！
echo ========================================
echo 请检查安装是否正确完成
echo.

:end
echo.
echo 按任意键退出...
pause >nul
