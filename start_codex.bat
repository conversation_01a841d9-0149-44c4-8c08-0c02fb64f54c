@echo off
echo 正在启动 Codex CLI...
echo.

REM 设置环境变量
set PATH=%PATH%;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm

REM 检查 codex 是否可用
codex --version
if %errorlevel% neq 0 (
    echo 错误：Codex CLI 未正确安装或配置
    pause
    exit /b 1
)

echo.
echo Codex CLI 已准备就绪！
echo.
echo 使用方法：
echo   codex                    - 启动交互式会话
echo   codex "你的问题"         - 直接提问
echo   codex exec "你的问题"    - 非交互式执行
echo   codex login              - 登录账户
echo   codex --help             - 查看帮助
echo.

REM 启动交互式会话
codex

REM 如果出现错误，保持窗口打开
if %errorlevel% neq 0 (
    echo.
    echo 出现错误，请检查配置
    pause
)

REM 正常结束也保持窗口打开一会儿
echo.
echo Codex CLI 会话已结束
pause
