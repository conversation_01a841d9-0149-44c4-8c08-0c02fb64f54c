import os
import pandas as pd
import glob
from datetime import datetime
import tushare as ts
import time

# 设置TuShare token
ts.set_token('e5991012344cb5807859d974da3d1a08a98f5c404bf8dace4e7e4ebe')
pro = ts.pro_api()

# 指定目录路径
directory_path = r'G:\i\stock\stock_data\stock-hk-stock-data'

# 查找所有CSV文件
csv_files = glob.glob(os.path.join(directory_path, "*.csv"))

# 获取当前日期
current_date = datetime.now()
current_date_str = current_date.strftime('%Y-%m-%d')
current_date_ts = current_date.strftime('%Y%m%d')

for i, file_path in enumerate(csv_files):
    try:
        # 读取CSV文件，跳过第一行描述信息
        df = pd.read_csv(file_path, encoding='gbk', skiprows=1)
        
        # 获取港股代码
        hk_stock_code = df['港股代码'].iloc[0] if not df.empty else None
        
        # 获取最后一行交易日期
        last_date_str = df['交易日期'].iloc[-1] if not df.empty else None
        
        print(f"处理文件: {file_path}")
        print(f"港股代码: {hk_stock_code}, 最后交易日期: {last_date_str}")
        
        if hk_stock_code and last_date_str:
            try:
                # 检查日期的合理性
                last_date = datetime.strptime(last_date_str, '%Y-%m-%d')
                
                # 如果最后日期是未来日期，使用当前日期往前推一年作为开始日期
                if last_date > current_date:
                    print(f"警告: 最后交易日期 {last_date_str} 是未来日期，使用当前日期前一年作为开始日期")
                    last_date = current_date.replace(year=current_date.year-1)
                    last_date_str = last_date.strftime('%Y-%m-%d')
                    last_date_ts = last_date.strftime('%Y%m%d')
                else:
                    last_date_ts = last_date.strftime('%Y%m%d')

                # 防止API调用频率过高，添加延时
                if i > 0:
                    print(f"等待30秒以遵守API频率限制...")
                    time.sleep(30)  # 每个请求间隔30秒
                
                # 使用TuShare通用接口获取最新的港股数据
                ts_code = hk_stock_code
                
                print(f"获取从 {last_date_str} 到 {current_date_str} 的数据...")
                new_data = pro.query('hk_daily', 
                                    ts_code=ts_code, 
                                    start_date=last_date_ts, 
                                    end_date=current_date_ts)
                
                if not new_data.empty:
                    # 转换TuShare数据的列名以匹配原数据
                    new_data = new_data.rename(columns={
                        'trade_date': '交易日期',
                        'open': '开盘价',
                        'high': '最高价', 
                        'low': '最低价',
                        'close': '收盘价',
                        'pre_close': '前收盘价'
                    })
                    
                    # 日期格式转换 (YYYYMMDD -> YYYY-MM-DD)
                    new_data['交易日期'] = pd.to_datetime(new_data['交易日期']).dt.strftime('%Y-%m-%d')
                    
                    # 添加缺少的列
                    new_data['股票代码'] = df['股票代码'].iloc[0]
                    new_data['港股代码'] = hk_stock_code
                    new_data['文件名'] = os.path.basename(file_path)
                    
                    # 仅保留比最后日期更新的数据
                    new_data = new_data[new_data['交易日期'] > last_date_str]
                    
                    if not new_data.empty:
                        # 确保必要的列都存在
                        required_columns = ['交易日期', '股票代码', '港股代码', '开盘价', 
                                           '最高价', '最低价', '收盘价', '前收盘价', '文件名']
                        
                        # 确保所有必需的列都存在
                        missing_columns = [col for col in required_columns if col not in new_data.columns]
                        if missing_columns:
                            # 如果有缺失列，尝试从原始数据中获取
                            for col in missing_columns:
                                if col in df.columns:
                                    new_data[col] = df[col].iloc[-1]
                                else:
                                    print(f"警告: 无法获取列 {col}，使用空值")
                                    if col in ['开盘价', '最高价', '最低价', '收盘价', '前收盘价']:
                                        new_data[col] = 0.0
                                    else:
                                        new_data[col] = ''
                                
                        # 选择必要的列并确保顺序一致
                        new_data = new_data[required_columns]
                        
                        print(f"获取到 {len(new_data)} 条新记录")
                        
                        # 合并数据并保存
                        combined_df = pd.concat([df, new_data])
                        
                        # 保存带有描述行的文件
                        with open(file_path, 'r', encoding='gbk') as file:
                            header_line = file.readline().strip()
                        
                        # 先保存DataFrame
                        temp_file = f"temp_{os.path.basename(file_path)}"
                        combined_df.to_csv(temp_file, index=False, encoding='gbk')
                        
                        # 然后插入描述行
                        with open(temp_file, 'r', encoding='gbk') as temp:
                            content = temp.read()
                            
                        with open(file_path, 'w', encoding='gbk') as file:
                            file.write(header_line + '\n' + content)
                            
                        # 删除临时文件
                        os.remove(temp_file)
                        
                        print(f"已更新数据到: {file_path}, 添加了 {len(new_data)} 条新记录")
                    else:
                        print(f"没有新的数据需要添加")
                else:
                    print(f"未能获取 {hk_stock_code} 的新数据")
            
            except Exception as e:
                print(f"获取新数据时出错: {e}")
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        
    print("-" * 60)  # 分隔线
    
print("所有文件处理完成")