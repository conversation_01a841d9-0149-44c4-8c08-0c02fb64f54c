# Codex CLI 启动脚本
Write-Host "正在启动 Codex CLI..." -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:PATH += ";C:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm"

# 检查 codex 是否可用
try {
    $version = & codex --version 2>$null
    Write-Host "✅ $version" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误：Codex CLI 未正确安装或配置" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "🚀 Codex CLI 已准备就绪！" -ForegroundColor Cyan
Write-Host ""
Write-Host "使用方法：" -ForegroundColor Yellow
Write-Host "  codex                    - 启动交互式会话"
Write-Host "  codex `"你的问题`"         - 直接提问"
Write-Host "  codex exec `"你的问题`"    - 非交互式执行"
Write-Host "  codex login              - 登录账户"
Write-Host "  codex --help             - 查看帮助"
Write-Host ""

# 启动交互式会话
Write-Host "正在启动交互式会话..." -ForegroundColor Green
& codex
