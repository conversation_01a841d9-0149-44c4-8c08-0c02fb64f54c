@echo off
title Codex CLI - Ready to Use
echo.
echo ==========================================
echo            Codex CLI Ready!
echo ==========================================
echo.
echo Your authentication is already configured!
echo Account: <EMAIL>
echo Plan: Team
echo.
echo Available commands:
echo   codex                    - Start interactive session
echo   codex exec "question"    - Ask directly
echo   codex --help             - Show help
echo.
echo ==========================================
echo.

set PATH=%PATH%;C:\Program Files\nodejs

if "%1"=="" (
    echo Type commands below or press Ctrl+C to exit:
    echo.
    cmd /k "set PATH=%PATH%;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm"
) else (
    set PATH=%PATH%;C:\Users\<USER>\AppData\Roaming\npm
    "C:\Users\<USER>\AppData\Roaming\npm\codex.cmd" %*
    pause
)
